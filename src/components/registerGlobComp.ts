import type { App } from 'vue'
import { But<PERSON> } from './Button'
import {
  Input,
  Layout,
  Table,
  Image,
  Form,
  Row,
  Col,
  DatePicker,
  Select,
  Descriptions,
  Tag,
  Radio,
  InputNumber,
  Badge,
  Tooltip,
  Upload,
  Card,
  Modal,
  Spin,
  Space,
  Popover,
  Typography,
  Popconfirm,
  Rate,
  Avatar,
} from 'ant-design-vue'

export function registerGlobComp(app: App) {
  app
    .use(Input)
    .use(Button)
    .use(Layout)
    .use(Table)
    .use(Image)
    .use(Form)
    .use(Row)
    .use(Col)
    .use(DatePicker)
    .use(Select)
    .use(Descriptions)
    .use(Tag)
    .use(Radio)
    .use(InputNumber)
    .use(Badge)
    .use(Tooltip)
    .use(Upload)
    .use(Card)
    .use(Modal)
    .use(Spin)
    .use(Space)
    .use(Popover)
    .use(Typography)
    .use(Popconfirm)
    .use(Rate)
    .use(Avatar)
}
