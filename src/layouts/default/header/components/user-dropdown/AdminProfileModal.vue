<template>
  <BasicModal
    v-bind="$attrs"
    width="600px"
    :wrapperFooterOffset="1"
    useWrapper
    @register="register"
    title="编辑个人信息"
    :ok-button-props="{ disabled: disabled }"
    ok-text="保存"
    @cancel="onReset"
    @ok="onSubmit"
  >
    <div class="pt-3px pr-3px">
      <a-form
        ref="formRef"
        :model="formState"
        :rules="rules"
        :label-col="labelCol"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="姓名" name="name">
              <a-input v-model:value="formState.name" placeholder="请输入姓名" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="职位" name="position">
              <a-input v-model:value="formState.position" placeholder="请输入职位" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="头像" name="avatar">
              <div class="clearfix">
                <a-upload
                  v-model:file-list="avatarFileList"
                  accept="image/*"
                  name="files"
                  :headers="headers"
                  list-type="picture-card"
                  :action="uploadUrl"
                  @change="handleAvatarChange"
                  @preview="handleAvatarPreview"
                >
                  <div v-if="avatarFileList?.length < 1">
                    <plus-outlined />
                    <div style="margin-top: 8px">上传头像</div>
                  </div>
                </a-upload>
                <a-modal
                  :visible="avatarPreviewVisible"
                  :footer="null"
                  @cancel="handleAvatarPreviewCancel"
                >
                  <img alt="头像预览" style="width: 100%" :src="avatarPreviewUrl" />
                </a-modal>
              </div>
            </a-form-item>
          </a-col>
          
          <a-col :span="12">
            <a-form-item label="签名" name="signature">
              <div class="clearfix">
                <a-upload
                  v-model:file-list="signatureFileList"
                  accept="image/*"
                  name="files"
                  :headers="headers"
                  list-type="picture-card"
                  :action="uploadUrl"
                  @change="handleSignatureChange"
                  @preview="handleSignaturePreview"
                >
                  <div v-if="signatureFileList?.length < 1">
                    <plus-outlined />
                    <div style="margin-top: 8px">上传签名</div>
                  </div>
                </a-upload>
                <a-modal
                  :visible="signaturePreviewVisible"
                  :footer="null"
                  @cancel="handleSignaturePreviewCancel"
                >
                  <img alt="签名预览" style="width: 100%" :src="signaturePreviewUrl" />
                </a-modal>
              </div>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { BasicModal, useModalInner } from '/@/components/Modal'
  import { computed, reactive, ref } from 'vue'
  import { PlusOutlined } from '@ant-design/icons-vue'
  import { message, type FormInstance, type UploadChangeParam } from 'ant-design-vue'
  import { useGlobSetting } from '/@/hooks/setting'
  import { getToken } from '/@/utils/auth'
  import { useUserStore } from '/@/store/modules/user'
  import type { AdminProfileModel } from '/@/api/sys/model/userModel'

  const emit = defineEmits(['finished', 'register'])
  
  const userStore = useUserStore()
  const formRef = ref<FormInstance>()
  const labelCol = { style: { width: '80px' } }

  // 表单状态
  const formState = reactive({
    name: '',
    position: '',
    avatarId: null as number | null,
    signatureId: null as number | null,
  })

  // 表单验证规则
  const rules = {
    name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
    position: [{ required: true, message: '请输入职位', trigger: 'blur' }],
  }

  // 上传配置
  const headers = {
    Authorization: `Bearer ${getToken()}`,
  }
  const uploadUrl = useGlobSetting().apiUrl + '/upload'

  // 头像上传相关
  const avatarFileList = ref([])
  const avatarPreviewVisible = ref(false)
  const avatarPreviewUrl = ref('')

  // 签名上传相关
  const signatureFileList = ref([])
  const signaturePreviewVisible = ref(false)
  const signaturePreviewUrl = ref('')

  // 表单验证状态
  const disabled = computed(() => {
    return !formState.name || !formState.position
  })

  // 弹窗注册
  const [register, { closeModal, changeOkLoading }] = useModalInner((data: AdminProfileModel) => {
    if (data) {
      onDataReceive(data)
    }
  })

  // 接收数据并初始化表单
  const onDataReceive = (data: AdminProfileModel) => {
    formState.name = data.name || ''
    formState.position = data.position || ''
    
    // 初始化头像
    if (data.avatar?.url) {
      avatarFileList.value = [{
        uid: '-1',
        name: 'avatar.png',
        status: 'done',
        url: data.avatar.url,
      }]
    }
    
    // 初始化签名
    if (data.signature?.url) {
      signatureFileList.value = [{
        uid: '-2',
        name: 'signature.png',
        status: 'done',
        url: data.signature.url,
      }]
    }
  }

  // 头像上传处理
  const handleAvatarChange = (info: UploadChangeParam) => {
    if (info.file.status === 'done') {
      const id = info.file.response[0].id
      formState.avatarId = id
      message.success(`头像上传成功`)
    } else if (info.file.status === 'error') {
      message.error(`头像上传失败`)
    }
  }

  const handleAvatarPreview = async (file) => {
    avatarPreviewUrl.value = file.url || file.preview
    avatarPreviewVisible.value = true
  }

  const handleAvatarPreviewCancel = () => {
    avatarPreviewVisible.value = false
  }

  // 签名上传处理
  const handleSignatureChange = (info: UploadChangeParam) => {
    if (info.file.status === 'done') {
      const id = info.file.response[0].id
      formState.signatureId = id
      message.success(`签名上传成功`)
    } else if (info.file.status === 'error') {
      message.error(`签名上传失败`)
    }
  }

  const handleSignaturePreview = async (file) => {
    signaturePreviewUrl.value = file.url || file.preview
    signaturePreviewVisible.value = true
  }

  const handleSignaturePreviewCancel = () => {
    signaturePreviewVisible.value = false
  }

  // 重置表单
  const onReset = () => {
    formRef.value?.resetFields()
    avatarFileList.value = []
    signatureFileList.value = []
    formState.avatarId = null
    formState.signatureId = null
  }

  // 提交表单
  const onSubmit = async () => {
    try {
      await formRef.value?.validate()
      
      if (!formState.avatarId || !formState.signatureId) {
        message.error('请上传头像和签名')
        return
      }

      changeOkLoading(true)
      
      await userStore.updateAdminProfileAction({
        name: formState.name,
        position: formState.position,
        avatar: { id: formState.avatarId },
        signature: { id: formState.signatureId },
      })
      
      message.success('个人信息更新成功')
      closeModal()
      emit('finished')
    } catch (error) {
      console.error('更新失败:', error)
      message.error('更新失败，请重试')
    } finally {
      changeOkLoading(false)
    }
  }
</script>

<style scoped>
  .clearfix::after {
    content: '';
    display: table;
    clear: both;
  }
</style>
